#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据上传到MySQL脚本
将6.1-7.17.xlsx中的数据上传到MySQL数据库，跳过重复数据
"""

import pandas as pd
import pymysql
import sys
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upload_log.txt', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 3306,
    'user': 'Seller',
    'password': '98c06z27W@',
    'database': 'kkuguan_db',
    'charset': 'utf8mb4'
}

# Excel文件路径
EXCEL_FILE = '6.1-7.17.xlsx'

# 目标表名
TABLE_NAME = 'orders'

# 字段映射（Excel列名对应MySQL列名）
COLUMN_MAPPING = {
    'platform_id': 'platform_id',
    'order_number': 'order_number', 
    'original_sku': 'original_sku',
    'asin': 'asin',
    'total_products': 'total_products',
    'product_quantity': 'product_quantity',
    'multi_product_name': 'multi_product_name',
    'order_date': 'order_date',
    'platform_channel': 'platform_channel',
    'store_account': 'store_account',
    'sku': 'sku'
}

def connect_to_mysql():
    """连接到MySQL数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.info("成功连接到MySQL数据库")
        return connection
    except Exception as e:
        logger.error(f"连接MySQL数据库失败: {e}")
        return None

def read_excel_data():
    """读取Excel文件数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(EXCEL_FILE)
        logger.info(f"成功读取Excel文件，共{len(df)}行数据")
        
        # 显示列名
        logger.info(f"Excel文件列名: {list(df.columns)}")
        
        # 只保留需要的列
        required_columns = list(COLUMN_MAPPING.keys())
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            logger.warning(f"Excel文件中缺少以下列: {missing_columns}")
            # 只保留存在的列
            available_columns = [col for col in required_columns if col in df.columns]
            df = df[available_columns]
        else:
            df = df[required_columns]
        
        # 处理空值
        df = df.fillna('')
        
        logger.info(f"处理后的数据形状: {df.shape}")
        return df
        
    except Exception as e:
        logger.error(f"读取Excel文件失败: {e}")
        return None

def check_duplicate_record(cursor, record):
    """检查记录是否已存在（所有字段都相同）"""
    try:
        # 构建WHERE条件，检查所有字段
        conditions = []
        values = []
        
        for excel_col, mysql_col in COLUMN_MAPPING.items():
            if excel_col in record:
                if pd.isna(record[excel_col]) or record[excel_col] == '':
                    conditions.append(f"({mysql_col} IS NULL OR {mysql_col} = '')")
                else:
                    conditions.append(f"{mysql_col} = %s")
                    values.append(record[excel_col])
        
        where_clause = " AND ".join(conditions)
        query = f"SELECT COUNT(*) FROM {TABLE_NAME} WHERE {where_clause}"
        
        cursor.execute(query, values)
        count = cursor.fetchone()[0]
        
        return count > 0
        
    except Exception as e:
        logger.error(f"检查重复记录失败: {e}")
        return False

def insert_record(cursor, record):
    """插入单条记录"""
    try:
        # 构建INSERT语句
        columns = []
        placeholders = []
        values = []
        
        for excel_col, mysql_col in COLUMN_MAPPING.items():
            if excel_col in record:
                columns.append(mysql_col)
                placeholders.append('%s')
                values.append(record[excel_col] if not pd.isna(record[excel_col]) else None)
        
        columns_str = ', '.join(columns)
        placeholders_str = ', '.join(placeholders)
        
        query = f"INSERT INTO {TABLE_NAME} ({columns_str}) VALUES ({placeholders_str})"
        
        cursor.execute(query, values)
        return True
        
    except Exception as e:
        logger.error(f"插入记录失败: {e}")
        return False

def upload_data_to_mysql(df):
    """上传数据到MySQL"""
    connection = connect_to_mysql()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        total_records = len(df)
        inserted_count = 0
        duplicate_count = 0
        error_count = 0
        
        logger.info(f"开始处理{total_records}条记录...")
        
        for index, record in df.iterrows():
            try:
                # 检查是否重复
                if check_duplicate_record(cursor, record):
                    duplicate_count += 1
                    logger.debug(f"第{index+1}行: 重复记录，跳过")
                    continue
                
                # 插入记录
                if insert_record(cursor, record):
                    inserted_count += 1
                    logger.debug(f"第{index+1}行: 成功插入")
                else:
                    error_count += 1
                    logger.warning(f"第{index+1}行: 插入失败")
                
                # 每100条记录提交一次
                if (index + 1) % 100 == 0:
                    connection.commit()
                    logger.info(f"已处理{index+1}条记录...")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"第{index+1}行处理失败: {e}")
        
        # 最终提交
        connection.commit()
        
        logger.info("=" * 50)
        logger.info("数据上传完成!")
        logger.info(f"总记录数: {total_records}")
        logger.info(f"成功插入: {inserted_count}")
        logger.info(f"重复跳过: {duplicate_count}")
        logger.info(f"错误记录: {error_count}")
        logger.info("=" * 50)
        
        return True
        
    except Exception as e:
        logger.error(f"上传数据失败: {e}")
        return False
        
    finally:
        if connection:
            connection.close()
            logger.info("数据库连接已关闭")

def main():
    """主函数"""
    logger.info("开始执行Excel数据上传任务...")
    
    # 读取Excel数据
    df = read_excel_data()
    if df is None:
        logger.error("读取Excel数据失败，程序退出")
        return
    
    # 上传数据到MySQL
    success = upload_data_to_mysql(df)
    
    if success:
        logger.info("任务执行成功!")
    else:
        logger.error("任务执行失败!")

if __name__ == "__main__":
    main()
